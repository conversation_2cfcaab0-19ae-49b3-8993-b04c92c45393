import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// 日志配置管理
/// 
/// 根据不同的构建环境（development, staging, production）
/// 提供相应的日志配置策略
class LogConfig {
  /// 获取当前环境的日志级别
  static Level getLogLevel() {
    if (kReleaseMode) {
      // 生产环境：只记录警告及以上级别
      return Level.warning;
    } else if (kProfileMode) {
      // 性能测试环境：记录信息及以上级别
      return Level.info;
    } else {
      // 开发环境：记录所有级别（包括调试信息）
      return Level.trace;
    }
  }

  /// 获取当前环境名称
  static String getEnvironmentName() {
    if (kReleaseMode) {
      return 'Production';
    } else if (kProfileMode) {
      return 'Profile';
    } else {
      return 'Debug';
    }
  }

  /// 是否启用彩色输出
  static bool get enableColors => kDebugMode;

  /// 是否启用表情符号
  static bool get enableEmojis => kDebugMode;

  /// 是否显示方法调用栈
  static bool get showMethodCount => kDebugMode;

  /// 方法调用栈显示层数
  static int get methodCount => kDebugMode ? 2 : 0;

  /// 错误时方法调用栈显示层数
  static int get errorMethodCount => kDebugMode ? 8 : 0;

  /// 日志行宽度
  static int get lineLength => 120;

  /// 是否显示时间戳
  static DateTimeFormat get dateTimeFormat {
    if (kDebugMode) {
      return DateTimeFormat.onlyTimeAndSinceStart;
    } else {
      return DateTimeFormat.none;
    }
  }

  /// 初始化全局日志级别
  static void initialize() {
    Logger.level = getLogLevel();
    
    if (kDebugMode) {
      // 在调试模式下输出初始化信息
      final logger = Logger(
        printer: PrettyPrinter(
          methodCount: 0,
          colors: true,
          printEmojis: true,
        ),
      );
      
      logger.i('''
🚀 Logger initialized
Environment: ${getEnvironmentName()}
Log Level: ${getLogLevel().name}
Colors: ${enableColors ? 'Enabled' : 'Disabled'}
Emojis: ${enableEmojis ? 'Enabled' : 'Disabled'}
''');
    }
  }
}
